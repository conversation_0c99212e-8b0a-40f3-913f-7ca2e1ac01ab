<?php
namespace App\Http\Requests;

use App\Services\ApplyPreregistrationService;
use App\Services\GameImageApplyService;
use Illuminate\Http\JsonResponse;
use Validator;

/**
 * 事前登録申請リクエスト
 */
class ApplyPreregistrationRequest extends Request
{

    /** @var ApplyPreregistrationService */
    protected $service;

    /** @var GameImageApplyService */
    protected $gameImageApplyService;

    public function __construct(
        ApplyPreregistrationService $service,
        GameImageApplyService $gameImageApplyService)
    {
        $this->service = $service;
        $this->gameImageApplyService = $gameImageApplyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {

        // ゲーム内画像申請によるファイルアップロードバリデーション
        Validator::extend('is_file_upload', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isExaminationFileUpload($this->route('id'), $this->route('device'), $attribute);
        });
        // ゲーム情報入力：メールアドレスバリデーション
        Validator::extend('is_registered_contact_mail_address', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isRegisteredContactMailAddress($this->route('id'));
        });
        // ゲーム情報入力：デバイスごとのゲーム情報バリデーション（紹介文等）
        Validator::extend('is_registered_game_info', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isRegisteredGameInfo($this->route('id'), $this->route('device'), $attribute);
        });

        // ゲーム管理：ゲーム画像 動画設定・申請 サムネイル画像登録確認
        Validator::extend('is_thumbnail_registered', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isThumbnailImageRegistered($this->route('id'), $parameters[0], $this->route('device'));
        });

        // ゲーム管理：ゲーム画像 動画設定・申請
        Validator::extend('is_apply_image', function ($attribute, $value, $parameters, $validator) {
            $param = [
                'app_id' => $this->route('id'),
                'image_type' => $parameters[0],
                'examination_result' => 1, // OK
                'examination_situation' => 3, // 完了
            ];
            if(!empty($parameters[1])) $param['image_size'] = $parameters[1];
            if(!empty($parameters[2])) $param['device'] = $parameters[2];
            $count = $this->gameImageApplyService->getApplyCount($param);
            return $count > 0;
        });

        // コミュニティの作成確認
        Validator::extend('is_community_create', function ($attribute, $value, $parameters, $validator) {
            $appId = $this->route('id');
            return $this->service->isCommunityCreate($appId);
        });

        // トピックの作成確認
        Validator::extend('is_topic_create', function ($attribute, $value, $parameters, $validator) {
            $appId = $this->route('id');
            return $this->service->isTopicCreate($appId);
        });

        // サイズユニット確認
        Validator::extend('of_size_unit', function ($attribute, $value, $parameters, $validator) {
            return $this->service->ofSizeUnit($value);
        });

        // CERO区分確認
        Validator::extend('of_classification', function ($attribute, $value, $parameters, $validator) {
            return $this->service->ofClassification($value);
        });

        // CEROコンテンツアイコン確認
        Validator::extend('of_content_icon', function ($attribute, $value, $parameters, $validator) {
            return $this->service->ofContentIcon($value);
        });

        $routeName = $this->getRouteName();
        switch ($routeName) {
            // 審査用の画像
            case 'Games.apply.preregistration.examinationimages': 
                return [
                    'similarity_check_materials' => 'is_file_upload',
                ];
                break;
            // ゲーム紹介ページ
            case 'Games.apply.preregistration.introductionimages':
                return [
                    'character_image' => 'is_file_upload',
                    'title_logo_image' => 'is_file_upload',
                    'background_image' => 'is_file_upload',
                    'catchphrase_image' => 'sometimes|required',
                    'catchphrase_image_text' => 'required_if:catchphrase_image,true|max:30',
                    'character_priority_notes' => 'sometimes|required',
                    'character_priority_notes_text' => 'required_if:character_priority_notes,true|max:400',
                    'copyright' => 'sometimes|required',
                    'copyright_text' => 'required_if:copyright,true|max:30',
                ];
                break;
            // プラットフォーム上に掲載される画像
            case 'Games.apply.preregistration.platformimages':
                $device = $this->route('device');
                // ソーシャルゲームのみ、サムネイル画像のサイズ指定キーが異なるため、条件分岐
                $gamesApplyDeviceTypeList = config('forms.Games.applyDeviceType');
                $isGames = in_array($device, $gamesApplyDeviceTypeList);
                $thumbnailImageRule = $isGames ?
                    'is_thumbnail_registered:1' :
                    'is_thumbnail_registered:200';
                // クライアントゲームとそれ以外でゲーム紹介画像の登録先が異なるのでルール設定
                $convertDeviceForApply = config('forms.GameImage.convertDeviceForApply');
                $clGamesApplyDeviceTypeList = config('forms.ClGames.applyDeviceType');
                $isClGames = in_array($device, $clGamesApplyDeviceTypeList);
                $gameIntroductionImageRule = $isClGames ? 'is_file_upload' : 'is_apply_image:2,,' . $convertDeviceForApply[$device];
                return [
                    'thumbnail_image' => $thumbnailImageRule,
                    'pre_release_small_banner' => 'is_file_upload',
                    'pre_release_big_banner' => 'is_file_upload',
                    'release_schedule_season_text' => 'sometimes|required|max:11',
                    'game_introduction_image' => $gameIntroductionImageRule,
                    'game_introduction_thumbnail' => 'is_file_upload',
                ];
            // 事前登録サイト
            case 'Games.apply.preregistration.preregistrationsite':
                return [
                    'design_delivery' => 'is_file_upload',
                    'is_following_terms_create' => 'sometimes|required',
                    'is_deploy_official_site' => 'sometimes|required',
                    'is_pre_register_check' => 'sometimes|required',
                    'means_verification_text' => 'sometimes|required|max:400',
                    'official_site_url' => 'sometimes|required|url|max:400'
                ];
            // サンドボックス環境 事前登録検証
            case 'Games.apply.preregistration.sandboxverification':
                return [
                    'is_user_type_special_processing_done' => 'sometimes|required',
                    'sandbox_test_game_app_id_text'        => 'sometimes|required|int',
                ];
            // 動作検証
            case 'Games.apply.preregistration.verification':
                return [
                    'is_production_testing_preparation_done' => 'sometimes|required',
                ];
            // ゲーム情報入力
            case 'Games.apply.preregistration.gameinformation':
                return [
                    'contact_mail_address'          => 'sometimes|is_registered_contact_mail_address',
                    'game_introduction'             => 'sometimes|is_registered_game_info',
                    'channeling_game_introduction'  => 'sometimes|is_registered_game_info',
                    'game_introduction_detail'      => 'sometimes|is_registered_game_info',
                    'supported_device'              => 'sometimes|is_registered_game_info',
                    'game_formal_name_text'         => 'sometimes|required|max:40',
                    'client_game_introduction_text' => 'sometimes|required|max:2048|forbidden_chars_in_encoding',
                    'pre_release_game_announcement' => 'sometimes|required',
                    'recommendation_age_division'   => 'sometimes|required',
                ];
            // コミュニティ
            case 'Games.apply.preregistration.community':
                return [
                    'community_create' => 'sometimes|is_community_create',
                    'topic_create' => 'sometimes|is_topic_create',
                ];
            // Win対応環境
            case 'Games.apply.preregistration.windowssupportedenvironment':
                return [
                    'os_version_text' => 'sometimes|required|max:30',
                    'processor_text' => 'sometimes|required|max:30',
                    'memory_size' => 'sometimes|required|int',
                    'memory_size_unit' => 'sometimes|required|of_size_unit',
                    'graphics_text' => 'sometimes|required|max:30',
                    'capacity_size' => 'sometimes|required|int',
                    'capacity_size_unit' => 'sometimes|required|of_size_unit',
                    'note_text' => 'max:400',
                ];
            // Mac対応環境
            case 'Games.apply.preregistration.macsupportedenvironment':
                return [
                    'os_version_text' => 'max:30',
                    'processor_text' => 'max:30',
                    'memory_size' => 'int',
                    'memory_size_unit' => 'of_size_unit',
                    'graphics_text' => 'max:30',
                    'capacity_size' => 'int',
                    'capacity_size_unit' => 'of_size_unit',
                    'note_text' => 'max:400',
                ];
            // CERO
            case 'Games.apply.preregistration.cero':
                return [
                    'classification_text' => 'sometimes|of_classification',
                    'content_icons' => 'sometimes|of_content_icon',
                ];
            default:
                abort(400);
        }
    }

    public function attributes()
    {
        return [
            // 審査用の画像
            'similarity_check_materials' => '類似チェック用素材',
            // ゲーム紹介ページ
            'character_image' => 'キャラクター素材',
            'title_logo_image' => 'タイトルロゴ画像',
            'background_image' => '背景画像',
            'catchphrase_image' => 'キャッチコピー/イメージテキストの有無',
            'catchphrase_image_text' => 'キャッチコピー/イメージテキスト',
            'character_priority_notes' => 'キャラクターの優先度/注意事項の有無',
            'character_priority_notes_text' => 'キャラクターの優先度/注意事項',
            'copyright' => 'コピーライトの有無',
            'copyright_text' => 'コピーライト',
            // プラットフォーム上に掲載される画像
            'thumbnail_image' => 'サムネイル画像',
            'pre_release_small_banner' => 'リリース前バナー_小',
            'pre_release_big_banner' => 'リリース前バナー_大',
            'release_schedule_season_text' => 'リリース予定時期',
            'game_introduction_image' => 'ゲーム紹介画像',
            'game_introduction_thumbnail' => 'ゲーム紹介サムネイル',
            // 事前登録サイト
            'design_delivery' => 'デザイン納品',
            'is_following_terms_create' => '公式サイトの作成',
            'is_deploy_official_site' => '事前登録用の公式サイトをサーバーに反映した',
            'is_pre_register_check' => '公式サイトから事前登録を行えることを確認した',
            'means_verification_text' => '公式サイトの確認方法',
            'official_site_url' => '事前登録時に公開するURL',
            // サンドボックス環境 事前登録検証
            'is_user_type_special_processing_done' => 'userTypeによる判別処理の動作を確認した',
            'sandbox_test_game_app_id_text' => 'サンドボックス環境のテスト用ゲームAppID',
            // 動作検証
            'is_production_testing_preparation_done' => '本番環境への繋ぎ込み',
            // ゲーム情報入力
            'contact_mail_address' => '問い合わせ先メールアドレス',
            'game_introduction' => '紹介文（20文字）',
            'channeling_game_introduction' => '紹介文（20文字）',
            'game_introduction_detail' => '紹介文の詳細',
            'supported_device' => '対応機種',
            'pre_release_game_announcement' => 'リリース直前ゲームとして公表',
            'recommendation_age_division' => '推奨年齢区分',
            'game_formal_name_text' => 'ゲーム正式名称',
            'client_game_introduction_text' => '製品紹介',
            // コミュニティ
            'community_create' => 'コミュニティ', // エラー文言をわかりやすくするために画面項目名とあえて変えています
            'topic_create' => 'トピック', // エラー文言をわかりやすくするために画面項目名とあえて変えています
            // Win対応環境
            'os_version_text' => 'OSバージョン',
            'processor_text' => 'プロセッサ',
            'memory_size' => 'メモリ',
            'memory_size_unit' => 'メモリの単位',
            'graphics_text' => 'グラフィックス',
            'capacity_size' => 'ディスク空き容量',
            'capacity_size_unit' => 'ディスク空き容量の単位',
            'note_text' => '備考',
            // Mac対応環境
            'os_version_text' => 'OSバージョン',
            'processor_text' => 'プロセッサ',
            'memory_size' => 'メモリ',
            'memory_size_unit' => 'メモリの単位',
            'graphics_text' => 'グラフィックス',
            'capacity_size' => 'ディスク空き容量',
            'capacity_size_unit' => 'ディスク空き容量の単位',
            'note_text' => '備考',
            // CERO
            'classification_text' => 'CERO区分',
            'content_icons' => 'CEROコンテンツアイコン',
        ];
    }

    public function customMessages()
    {
        return [
            'is_file_upload' => $this->MSG341,
            'is_registered_contact_mail_address' => $this->MSG012,
            'is_registered_game_info' => $this->MSG012,
            'is_community_create' => $this->MSG345,
            'is_topic_create' => $this->MSG345,
            'of_size_unit' => $this->MSG264,
            'of_classification' => $this->MSG264,
            'of_content_icon' => $this->MSG264,
            'forbidden_chars_in_encoding' => $this->MSG346, 
            // ゲーム紹介ページ
            'catchphrase_image.required' => $this->MSG241,
            'character_priority_notes.required' => $this->MSG241,
            'copyright.required' => $this->MSG241,
            // プラットフォーム上に掲載される画像
            'thumbnail_image.is_thumbnail_registered' => $this->MSG342,
            'game_introduction_image.is_apply_image' => $this->MSG343,
            // 事前登録サイト
            'is_following_terms_create.required' => $this->MSG344,
            'is_deploy_official_site.required' => $this->MSG344,
            'is_pre_register_check.required' => $this->MSG344,
            // サンドボックス環境 事前登録検証
            'is_user_type_special_processing_done.required' => $this->MSG344,
            'sandbox_test_game_app_id_text.required' => $this->MSG241,
            'sandbox_test_game_app_id_text.int' => $this->MSG031,
            // 動作検証
            'is_production_testing_preparation_done.required' => $this->MSG344,
            // ゲーム情報入力
            'pre_release_game_announcement.required' => $this->MSG241,
            'recommendation_age_division.required' => $this->MSG241,
            // Win対応環境
            'memory_size.int' => $this->MSG031,
            'capacity_size.int' => $this->MSG031,
            // Mac対応環境
            'memory_size.int' => $this->MSG031,
            'capacity_size.int' => $this->MSG031,
        ];
    }
    
    public function response( array $errors )
    {
        $response = [
            'status' => 422,
            'message' => 'Bad Request',
            'errors'  => $errors,
        ];
        return new JsonResponse( $response, 422 );
    }
    
    /**
     * リクエストのルート名を取得する
     *
     * @return void
     */
    public function getRouteName(){
        $routeName = $this->route()->getName();
        return $routeName;
    }
}
