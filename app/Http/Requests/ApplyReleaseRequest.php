<?php
namespace App\Http\Requests;

use App\Services\ApplyReleaseService;
use App\Services\GameImageApplyService;
use Illuminate\Http\JsonResponse;
use Validator;

/**
 * リリース申請リクエスト
 */
class ApplyReleaseRequest extends Request
{

    /** @var ApplyReleaseService */
    protected $service;

    /** @var GameImageApplyService */
    protected $gameImageApplyService;

    public function __construct(
        ApplyReleaseService $service,
        GameImageApplyService $gameImageApplyService)
    {
        $this->service = $service;
        $this->gameImageApplyService = $gameImageApplyService;
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {

        // ゲーム内画像申請によるファイルアップロードバリデーション
        Validator::extend('is_file_upload', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isExaminationFileUpload($this->route('id'), $this->route('device'), $attribute);
        });
        // ゲーム情報入力：メールアドレスバリデーション
        Validator::extend('is_registered_contact_mail_address', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isRegisteredContactMailAddress($this->route('id'));
        });
        // ゲーム情報入力：デバイスごとのゲーム情報バリデーション（紹介文等）
        Validator::extend('is_registered_game_info', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isRegisteredGameInfo($this->route('id'), $this->route('device'), $attribute);
        });
        // ゲーム管理：ゲーム画像 動画設定・申請 サムネイル画像登録確認
        Validator::extend('is_thumbnail_registered', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isThumbnailImageRegistered($this->route('id'), $parameters[0], $this->route('device'));
        });
        // ゲーム管理：ゲーム画像 動画設定・申請 サムネイル（総合トップ等）画像登録確認
        Validator::extend('is_overall_rated_thumbnail_registered', function ($attribute, $value, $parameters, $validator) {
            return $this->service->isOverallRatedThumbnailImageRegistered($this->route('id'), $parameters[0], $this->route('device'));
        });
        // ゲーム管理：ゲーム画像 動画設定・申請 その他
        Validator::extend('is_apply_image', function ($attribute, $value, $parameters, $validator) {
            $param = [
                'app_id' => $this->route('id'),
                'image_type' => $parameters[0],
                'examination_result' => 1, // OK
                'examination_situation' => 3, // 完了
            ];
            if(!empty($parameters[1])) $param['image_size'] = $parameters[1];
            if(!empty($parameters[2])) $param['device'] = $parameters[2];
            $count = $this->gameImageApplyService->getApplyCount($param);
            return $count > 0;
        });

        // コミュニティの作成確認
        Validator::extend('is_community_create', function ($attribute, $value, $parameters, $validator) {
            $appId = $this->route('id');
            return $this->service->isCommunityCreate($appId);
        });

        // トピックの作成確認
        Validator::extend('is_topic_create', function ($attribute, $value, $parameters, $validator) {
            $appId = $this->route('id');
            return $this->service->isTopicCreate($appId);
        });

        // サイズユニット確認
        Validator::extend('of_size_unit', function ($attribute, $value, $parameters, $validator) {
            return $this->service->ofSizeUnit($value);
        });

        // CERO区分確認
        Validator::extend('of_classification', function ($attribute, $value, $parameters, $validator) {
            return $this->service->ofClassification($value);
        });

        // CEROコンテンツアイコン確認
        Validator::extend('of_content_icon', function ($attribute, $value, $parameters, $validator) {
            return $this->service->ofContentIcon($value);
        });

        $routeName = $this->getRouteName();
        switch ($routeName) {
            // 審査用の画像
            case 'Games.apply.release.examinationimages': 
                return [
                    'similarity_check_materials' => 'is_file_upload',
                    'ethical_check_materials' => 'is_file_upload'
                ];
            // ゲーム紹介ページ
            case 'Games.apply.release.introductionimages': 
                return [
                    'character_image' => 'is_file_upload',
                    'title_logo_image' => 'is_file_upload',
                    'background_image' => 'is_file_upload',
                    'catchphrase_image' => 'sometimes|required',
                    'catchphrase_image_text' => 'required_if:catchphrase_image,true|max:30',
                    'character_priority_notes' => 'sometimes|required',
                    'character_priority_notes_text' => 'required_if:character_priority_notes,true|max:400',
                    'copyright' => 'sometimes|required',
                    'copyright_text' => 'required_if:copyright,true|max:30',
                ];
            // プラットフォーム上に掲載される画像
            case 'Games.apply.release.platformimages':
                $device = $this->route('device');
                // ソーシャルゲームのみ、サムネイル画像のサイズ指定キーが異なるため、条件分岐
                $gamesApplyDeviceTypeList = config('forms.Games.applyDeviceType');
                $isGames = in_array($device, $gamesApplyDeviceTypeList);
                $thumbnailImageRule = $isGames ?
                    'is_thumbnail_registered:1' :
                    'is_thumbnail_registered:200';
                // クライアントゲームとそれ以外でゲーム紹介画像の登録先が異なるのでルール設定
                $convertDeviceForApply = config('forms.GameImage.convertDeviceForApply');
                $clGamesApplyDeviceTypeList = config('forms.ClGames.applyDeviceType');
                $isClGames = in_array($device, $clGamesApplyDeviceTypeList);
                $gameIntroductionImageRule = $isClGames ? 'is_file_upload' : 'is_apply_image:2,,' . $convertDeviceForApply[$device];
                $overallRatedThumbnailImageRule = $isGames ?
                    'is_overall_rated_thumbnail_registered:7' :
                    'is_overall_rated_thumbnail_registered:400';
                return [
                    'thumbnail_image' => $thumbnailImageRule,
                    'game_introduction_image' => $gameIntroductionImageRule,
                    'game_introduction_thumbnail' => 'is_file_upload',
                    'overall_rated_thumbnail_image' => $overallRatedThumbnailImageRule,
                ];
            // 公式サイト検証
            case 'Games.apply.release.releasesite':
                return [
                    'design_delivery' => 'is_file_upload',
                    'is_following_terms_create' => 'sometimes|required',
                    'means_verification_text'   => 'sometimes|required|max:400',
                    'official_site_url_text'    => 'sometimes|required|url|max:400',
                ];
            // 動作検証
            case 'Games.apply.release.verification':
                $rules = [
                    'apk_upload'                        => 'sometimes|is_file_upload',
                    'is_bug_check_done'                 => 'sometimes|required',
                    'is_game_testing_done'              => 'sometimes|required',
                    'is_finished_product'               => 'sometimes|required',
                ];
                $device = $this->route('device');
                if ($device == 'client') {
                    $rules['is_examination_check_done'] = 'sometimes|required';
                }
                $rules += [
                    'game_start_url'                    => 'sometimes|required|url|max:400',
                    'is_other_works_used_and_un_morality_expression_un_used'
                                                        => 'sometimes|required',
                    'is_following_terms_develop'        => 'sometimes|required',
                    'is_following_terms_management'     => 'sometimes|required',
                    'in_game_virtual_currency'          => 'sometimes|required',
                    'is_following_terms_of_service'     => 'sometimes|required_if:in_game_virtual_currency,true',
                    'using_inspection_api'              => 'sometimes|required',
                    'inspection_api_verification_method'
                                                        => 'sometimes|required_if:using_inspection_api,true|max:400',
                    'tags_to_set_in_game'               => 'sometimes|required|max:5',
                    'is_signed_by_digital_signature'    => 'sometimes|required',
                    'is_using_external_installer'       => 'sometimes|required',
                    'is_using_module'                   => 'sometimes|required',
                    'has_trade_feature_in_game'         => 'sometimes|required',
                    'has_gamechip'                      => 'required_if:has_trade_feature_in_game,true',
                    'ngword_version_text'               => 'sometimes|required',
                    'has_ngword_check'                  => 'sometimes|required',
                    'is_monthly_payment_service_or_subscription' => 'sometimes|required',
                ];
                return $rules;
            // ゲーム情報入力
            case 'Games.apply.release.gameinformation':
                return [
                    'contact_mail_address'          => 'sometimes|is_registered_contact_mail_address',
                    'game_introduction'             => 'sometimes|is_registered_game_info',
                    'channeling_game_introduction'  => 'sometimes|is_registered_game_info',
                    'game_introduction_detail'      => 'sometimes|is_registered_game_info',
                    'supported_device'              => 'sometimes|is_registered_game_info',
                    'game_formal_name_text'         => 'sometimes|required|max:40',
                    'client_game_introduction_text' => 'sometimes|required|max:2048|forbidden_chars_in_encoding',
                    'recommendation_age_division'   => 'sometimes|required',
                ];
            // コミュニティ
            case 'Games.apply.release.community':
                return [
                    'community_create' => 'sometimes|is_community_create',
                    'topic_create' => 'sometimes|is_topic_create',
                ];
            // Linksmate
            case 'Games.apply.release.linksmate': 
                return [
                    'linksmate_title_logo_image' => 'is_file_upload',
                    'linksmate_copyright' => 'sometimes|required',
                    'linksmate_copyright_text' => 'required_if:linksmate_copyright,true|max:30',
                ];
            // Win対応環境
            case 'Games.apply.release.windowssupportedenvironment':
                return [
                    'os_version_text' => 'sometimes|required|max:30',
                    'processor_text' => 'sometimes|required|max:30',
                    'memory_size' => 'sometimes|required|int',
                    'memory_size_unit' => 'sometimes|required|of_size_unit',
                    'graphics_text' => 'sometimes|required|max:30',
                    'capacity_size' => 'sometimes|required|int',
                    'capacity_size_unit' => 'sometimes|required|of_size_unit',
                    'note_text' => 'max:400',
                ];
            // Mac対応環境
            case 'Games.apply.release.macsupportedenvironment':
                return [
                    'os_version_text' => 'max:30',
                    'processor_text' => 'max:30',
                    'memory_size' => 'int',
                    'memory_size_unit' => 'of_size_unit',
                    'graphics_text' => 'max:30',
                    'capacity_size' => 'int',
                    'capacity_size_unit' => 'of_size_unit',
                    'note_text' => 'max:400',
                ];
            // CERO
            case 'Games.apply.release.cero':
                return [
                    'classification_text' => 'sometimes|of_classification',
                    'content_icons' => 'sometimes|of_content_icon',
                ];
            default:
                abort(400);
        }
    }

    public function attributes()
    {
        return [
            // 審査用の画像
            'similarity_check_materials' => '類似チェック用素材',
            'ethical_check_materials' => '倫理チェック用素材',
            // ゲーム紹介ページ
            'character_image' => 'キャラクター素材',
            'title_logo_image' => 'タイトルロゴ画像',
            'background_image' => '背景画像',
            'catchphrase_image' => 'キャッチコピー/イメージテキストの有無',
            'catchphrase_image_text' => 'キャッチコピー/イメージテキスト',
            'character_priority_notes' => 'キャラクターの優先度/注意事項の有無',
            'character_priority_notes_text' => 'キャラクターの優先度/注意事項',
            'copyright' => 'コピーライトの有無',
            'copyright_text' => 'コピーライト',
            // プラットフォーム上に掲載される画像
            'thumbnail_image' => 'サムネイル画像',
            'game_introduction_image' => 'ゲーム紹介画像',
            'game_introduction_thumbnail' => 'ゲーム紹介サムネイル',
            'overall_rated_thumbnail_image' => 'サムネイル（総合トップ等）画像',
            // 公式サイト検証
            'design_delivery' => 'デザイン納品',
            'is_following_terms_create' => '公式サイト反映',
            'means_verification_text' => '公式サイトの確認方法',
            'official_site_url_text' => '公式サイトURL',
            // 動作検証
            'apk_upload' => 'APKファイルのアップロード',
            'is_bug_check_done' => 'ゲームのバグチェックが完了した',
            'is_game_testing_done' => '本番環境で正常にゲームが起動し、プレイが可能なことを確認した',
            'is_finished_product' => 'ゲームの完成品で、自社内の動作検証も終えている',
            'is_examination_check_done' => '審査提出の方法に基づいた準備が完了している',
            'game_start_url' => 'ゲーム起動できるURL',
            'is_other_works_used_and_un_morality_expression_un_used' => '他者著作物を無断利用/倫理基準に問題のある表現をしていないか',
            'is_following_terms_develop' => 'DMM GAMES Platformのガイドライン/規約を遵守したうえで開発完了',
            'is_following_terms_management' => 'DMM GAMES Platformのガイドライン/規約を遵守したうえで運営できるか',
            'in_game_virtual_currency' => 'DMMポイントで購入できる仮想通貨',
            'is_following_terms_of_service' => '仮想通貨の実装方法',
            'using_inspection_api' => 'Inspection APIの利用',
            'inspection_api_verification_method' => 'Inspection APIの確認方法',
            'tags_to_set_in_game' => 'ゲームに設定するタグ',
            'is_signed_by_digital_signature' => '起動プログラムにはデジタル署名が入っているか',
            'is_using_external_installer' => '外部(アン)インストーラーを使用しているか',
            'is_using_module' => 'モジュールを使用しているか',
            'has_trade_feature_in_game' => '条件に該当する取引機能がある/条件に該当する取引機能がない',
            'has_gamechip' => 'ゲームチップを導入している/ゲームチップを導入していない',
            'ngword_version_text' => '適用している「DMM提供のNGワード」のバージョンを記載してください',
            'has_ngword_check' => '「DMM提供のNGワード」の注意ワードについて監査する体制、もしくは入力制限を入れる等の対策を施しているか',
            'is_monthly_payment_service_or_subscription' => '月額決済サービスもしくは定期購入の有無',
            // ゲーム情報入力
            'contact_mail_address' => '問い合わせ先メールアドレス',
            'game_introduction' => '紹介文（20文字）',
            'channeling_game_introduction' => '紹介文（20文字）',
            'game_introduction_detail' => '紹介文',
            'supported_device' => '対応機種',
            'recommendation_age_division' => '推奨年齢区分',
            'game_formal_name_text' => 'ゲーム正式名称',
            'client_game_introduction_text' => '製品紹介',
            // コミュニティ
            'community_create' => 'コミュニティ', // エラー文言をわかりやすくするために画面項目名とあえて変えています
            'topic_create' => 'トピック', // エラー文言をわかりやすくするために画面項目名とあえて変えています
            // Linksmate
            'linksmate_title_logo_image' => 'タイトルロゴ',
            'linksmate_copyright' => 'コピーライトの有無',
            'linksmate_copyright_text' => 'コピーライト',
            // Win対応環境
            'os_version_text' => 'OSバージョン',
            'processor_text' => 'プロセッサ',
            'memory_size' => 'メモリ',
            'memory_size_unit' => 'メモリの単位',
            'graphics_text' => 'グラフィックス',
            'capacity_size' => 'ディスク空き容量',
            'capacity_size_unit' => 'ディスク空き容量の単位',
            'note_text' => '備考',
            // Mac対応環境
            'os_version_text' => 'OSバージョン',
            'processor_text' => 'プロセッサ',
            'memory_size' => 'メモリ',
            'memory_size_unit' => 'メモリの単位',
            'graphics_text' => 'グラフィックス',
            'capacity_size' => 'ディスク空き容量',
            'capacity_size_unit' => 'ディスク空き容量の単位',
            'note_text' => '備考',
            // CERO
            'classification_text' => 'CERO区分',
            'content_icons' => 'CEROコンテンツアイコン',
        ];
    }

    public function customMessages()
    {
        return [
            'is_file_upload' => $this->MSG341,
            'is_registered_contact_mail_address' => $this->MSG012,
            'is_registered_game_info' => $this->MSG012,
            'is_community_create' => $this->MSG345,
            'is_topic_create' => $this->MSG345,
            'of_size_unit' => $this->MSG264,
            'of_classification' => $this->MSG264,
            'of_content_icon' => $this->MSG264,
            'forbidden_chars_in_encoding' => $this->MSG346, 
            // プラットフォーム上に掲載される画像
            'catchphrase_image.required' => $this->MSG241,
            'character_priority_notes.required' => $this->MSG241,
            'copyright.required' => $this->MSG241,
            // プラットフォーム上に掲載される画像
            'thumbnail_image.is_thumbnail_registered' => $this->MSG342,
            'game_introduction_image.is_apply_image' => $this->MSG343,
            'overall_rated_thumbnail_image.is_overall_rated_thumbnail_registered' => $this->MSG342,
            // 公式サイト検証
            'is_following_terms_create.required' => $this->MSG344,
            // 動作検証
            'is_bug_check_done.required' => $this->MSG344,
            'is_game_testing_done.required' => $this->MSG344,
            'is_finished_product.required' => $this->MSG344,
            'is_other_works_used_and_un_morality_expression_un_used.required' => $this->MSG344,
            'is_following_terms_develop.required' => $this->MSG344,
            'is_following_terms_management.required' => $this->MSG344,
            'in_game_virtual_currency.required' => $this->MSG241,
            'is_following_terms_of_service.required_if' => $this->MSG241,
            'using_inspection_api.required' => $this->MSG241,
            'tags_to_set_in_game.required' => $this->MSG344,
            'tags_to_set_in_game.max' => $this->MSG340,
            'is_signed_by_digital_signature.required' => $this->MSG344,
            'is_using_external_installer.required' => $this->MSG241,
            'is_using_module.required' => $this->MSG241,
            'has_trade_feature_in_game.required' => $this->MSG241,
            'has_gamechip.required_if' => $this->MSG241,
            'has_ngword_check.required' => $this->MSG344,
            'is_monthly_payment_service_or_subscription.required' => $this->MSG241,
            // ゲーム情報入力
            'recommendation_age_division.required' => $this->MSG241,
            // Linksmate
            'linksmate_copyright.required' => $this->MSG241,
            // Win対応環境
            'memory_size.int' => $this->MSG031,
            'capacity_size.int' => $this->MSG031,
            // Mac対応環境
            'memory_size.int' => $this->MSG031,
            'capacity_size.int' => $this->MSG031,
        ];
    }
    
    public function response( array $errors )
    {
        $response = [
            'status' => 422,
            'message' => 'Bad Request',
            'errors'  => $errors,
        ];
        return new JsonResponse( $response, 422 );
    }
    
    /**
     * リクエストのルート名を取得する
     *
     * @return void
     */
    public function getRouteName(){
        $routeName = $this->route()->getName();
        return $routeName;
    }
}
