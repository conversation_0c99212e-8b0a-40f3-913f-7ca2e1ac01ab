<?php

namespace App\Libs\Apply;

use Carbon\Carbon;

/**
 * 事前登録申請情報
 */
class Preregistration extends ApplyCommon {

    protected $contentName = 'preRegistrationContent';

    // 審査項目別デバイス毎の必要有無MAP
    protected $items = [
        'examinationImages' => [
            // 類似チェック用素材
            'similarityCheckMaterials' =>
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
        ],
        // 【画像】ゲーム紹介ページ：デザイン部分素材
        'introductionImages' => [
            // キャラクター素材
            'characterImages' => 
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // タイトルロゴ画像
            'titleLogoImages' => 
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // 背景画像
            'backgroundImages' => 
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // スクリーンショット/イメージ図
            'screenshotImageDiagram' => 
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // キャッチコピー
            'catchphraseImageDiagram' => 
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // キャラクターの優先順/注意事項
            'isCharacterPriorityNotes' => 
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // コピーライト
            'copyright' => 
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // 【画像】プラットフォーム上に掲載される画像
        'platformImages' => [
            // サムネイル画像
            'thumbnailImage' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // リリース前バナー
            'preReleaseSmallBanner' =>
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => true,  'sp_channeling' => false, 'client' => true],
            // リリース前バナー_大
            'preReleaseBigBanner' =>
                ['pc' => true,  'sp' => false, 'android_app' => false, 'pc_channeling' => true,  'sp_channeling' => false, 'client' => true],
            // リリース予定時期
            'releaseScheduleSeason' =>
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true, 'client' => true],
            // ゲーム紹介動画
            'gameIntroductionMovie' => 
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ゲーム紹介画像
            'gameIntroductionImage' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ゲーム紹介サムネイル
            'gameIntroductionThumbnail' => 
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // Manifest icon Android 192px
            'android192x192' => 
                ['pc' => false, 'sp' => true,  'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // Manifest icon Android 512px
            'appleTouchIcon' => 
                ['pc' => false, 'sp' => true,  'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // Apple touch icon
            'android512x512' => 
                ['pc' => false, 'sp' => true,  'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // 【検証】事前登録用の公式サイト
        'preregistrationSite' => [
            // デザイン納品
            'designDelivery' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // 公式サイトの作成
            'createdOfficialSite' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // 公式サイトのサーバー反映
            'serverDeploy' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // 公式サイトの確認方法
            'meansVerification' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 公式サイトURL
            'officialSiteDetail' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
        ],
        //  【検証】サンドボックス環境 事前登録検証
        'sandboxVerification' => [
            // サンドボックス環境への繋ぎ込み
            'sandboxPreRegistrationVerifications' => 
                ['pc' => true,  'sp' => true,  'android_app' => false,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // サンドボックス用のテストゲームアプリID
            'sandboxTestGameAppId' => 
                ['pc' => true,  'sp' => true,  'android_app' => false,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // 【検証】動作検証
        'verification' => [
            // 事前登録の動作検証準備
            'preRegistrationVerifications' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // ゲーム情報入力
        'gameInformation' => [
            // 問い合わせ先メールアドレス
            'contactMailAddress' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // ゲーム正式名称
            'gameFormalName' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 紹介文（20文字）
            'gameIntroduction' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // 製品紹介
            'clientGameIntroduction' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 紹介文（20文字）/ジャンル
            'channelingGameIntroduction' => 
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true,  'sp_channeling' => true,  'client' => false],
            // 紹介文の詳細
            'gameIntroductionDetail' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // 対応機種
            'supportedDevice' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // リリース直前ゲームとして公表
            'preReleaseGameAnnouncement' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // 推奨年齢区分
            'recommendationAgeDivision' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
        ],
        // コミュニティ作成
        'community' => [
            // コミュニティの作成
            'communityCreate' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // トピックの作成
            'topicCreate' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // Win対応環境
        'windowsSupportedEnvironment' => [
            // OSバージョン
            'osVersion' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // プロセッサ
            'processor' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // メモリ
            'memory' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // グラフィック
            'graphics' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ディスク空き容量
            'diskFreeSpace' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 備考
            'note' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
        ],
        // Mac対応環境
        'macSupportedEnvironment' => [
            // OSバージョン
            'osVersion' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // プロセッサ
            'processor' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // メモリ
            'memory' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // グラフィック
            'graphics' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ディスク空き容量
            'diskFreeSpace' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 備考
            'note' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
        ],
        // CERO
        'cero' => [
            // CERO区分
            'classification' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // CEROコンテンツアイコン
            'contentIcons' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
        ],
    ];

    public function getExaminationImages($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('examinationImages', $param1, $param2, $default);
    }

    public function getIntroductionImages($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('introductionImages', $param1, $param2, $default);
    }

    public function getPlatformImages($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('platformImages', $param1, $param2, $default);
    }

    public function getPreregistrationSite($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('preregistrationSite', $param1, $param2, $default);
    }

    public function getVerification($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('verification', $param1, $param2, $default);
    }

    public function getSandboxVerification($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('sandboxVerification', $param1, $param2, $default);
    }

    public function getGameInformation($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('gameInformation', $param1, $param2, $default);
    }

    public function getWindowsSupportedEnvironment($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('windowsSupportedEnvironment', $param1, $param2, $default);
    }

    public function getMacSupportedEnvironment($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('macSupportedEnvironment', $param1, $param2, $default);
    }

    public function getCero($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('cero', $param1, $param2, $default);
    }

    public function getCommunity($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('community', $param1, $param2, $default);
    }
}