<?php

namespace App\Libs\Apply;

/**
 * リリース登録申請情報
 */
class Release extends ApplyCommon {

    protected $contentName = 'releaseContent';

    private $preregistration = null;

    // 審査項目別デバイス毎の必要有無MAP
    protected $items = [
        // 【画像】審査用の画像
        'examinationImages' => [
            // 類似チェック用素材
            'similarityCheckMaterials' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // 倫理チェック用素材
            'ethicalCheckMaterials' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
        ],
        // 【画像】ゲーム紹介ページ：デザイン部分素材
        'introductionImages' => [
            // キャラクター素材
            'characterImages' =>
                ['pc' => true, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // タイトルロゴ画像
            'titleLogoImages' =>
                ['pc' => true, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // 背景画像
            'backgroundImages' =>
                ['pc' => true, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // スクリーンショット/イメージの図
            'screenshotImageDiagram' =>
                ['pc' => true, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // キャッチコピー
            'catchphraseImageDiagram' =>
                ['pc' => true, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // キャラクターの優先順/注意事項
            'isCharacterPriorityNotes' =>
                ['pc' => true, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // コピーライト
            'copyright' =>
                ['pc' => true, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // 【画像】プラットフォーム上に掲載される画像
        'platformImages' => [
            // サムネイル画像
            'thumbnailImage' =>
                ['pc' => true, 'sp' => true,   'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // サムネイル（総合トップ等）画像
            'overallRatedThumbnailImage' =>
                ['pc' => true, 'sp' => true,   'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // ゲーム紹介動画
            'gameIntroductionMovie' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ゲーム紹介画像
            'gameIntroductionImage' =>
                ['pc' => true, 'sp' => true,   'android_app' => true,  'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ゲーム紹介サムネイル
            'gameIntroductionThumbnail' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // Manifest icon Android 192px
            'android192x192' =>
                ['pc' => false, 'sp' => true,  'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // Manifest icon Android 512px
            'android512x512' =>
                ['pc' => false, 'sp' => true,  'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // Apple touch icon
            'appleTouchIcon' =>
                ['pc' => false, 'sp' => true,  'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // 公式サイト検証
        'releaseSite' => [
            // デザイン納品
            'designDelivery' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // 公式サイト反映
            'createdOfficialSite' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // 公式サイトの確認方法
            'meansVerification' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // 公式サイトURL
            'officialSiteDetail' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
        ],
        // 動作検証
        'verification' => [
            // Apkファイルのアップロード
            'apkUpload' =>
                ['pc' => false, 'sp' => false, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // 本番環境でのゲーム動作
            'productionVerification' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // ゲーム起動できるURL
            'gameStartUrl' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => false],
            // 他者著作物を無断利用/倫理基準に問題のある表現をしていないか
            'otherWorksUsedAndUnMoralityExpressionUnUsed' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // DMM GAMES Platformのガイドライン/規約を遵守したうえで開発完了
            'followingTermsDevelop' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // DMM GAMES Platformのガイドライン/規約を遵守したうえで運営できるか
            'followingTermsManagement' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // DMMポイントで購入できる仮想通貨
            'gameVirtualCurrency' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // 仮想通貨の実装方法
            'implementsVirtualCurrency' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // Inspection APIの利用
            'usedInspectionApi' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // Inspection APIの確認方法
            'inspectionApiVerification' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // ゲームに設定するタグ
            'gamesTags' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => false],
            // 起動プログラムにはデジタル署名が入っているか
            'digitalSignature' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 外部(アン)インストーラーを使用しているか
            'externalInstaller' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // モジュールを使用しているか
            'module' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ゲーム内で条件に該当する取引機能があるか
            'tradeFeatureInGame' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 「DMM提供のNGワード」を適当している場合「NGワード」のバージョンを記載してください
            'ngwordVersion' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 「DMM提供のNGワード」を適当している場合「注意」ワードに関する監査する体制があるか
            'ngwordCheck' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 月額決済サービスもしくは定期購入の有無
            'monthlyPaymentServiceOrSubscription' =>
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
        ],
        // ゲーム情報入力
        'gameInformation' => [
            // 問い合わせ先メールアドレス
            'contactMailAddress' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // ゲーム正式名称
            'gameFormalName' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 紹介文（20文字）
            'gameIntroduction' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // 紹介文の詳細
            'gameIntroductionDetail' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // 製品紹介
            'clientGameIntroduction' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 紹介文（20文字）/ジャンル
            'channelingGameIntroduction' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => false],
            // 対応機種
            'supportedDevice' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // リリース直前ゲームとして公表
            'preReleaseGameAnnouncement' => 
                ['pc' => true,  'sp' => true,  'android_app' => true,  'pc_channeling' => true,  'sp_channeling' => true,  'client' => true],
            // 推奨年齢区分
            'recommendationAgeDivision' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
        ],
        // コミュニティ
        'community' => [
            // コミュニティの作成
            'communityCreate' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
            // トピックの作成
            'topicCreate' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => false],
        ],
        // Linksmate
        'linksmate' => [
            // タイトルロゴ
            'linksmateTitleLogoImages' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
            // コピーライト
            'linksmateCopyright' =>
                ['pc' => true, 'sp' => true, 'android_app' => true, 'pc_channeling' => true, 'sp_channeling' => true, 'client' => true],
        ],
        // Win対応環境
        'windowsSupportedEnvironment' => [
            // OSバージョン
            'osVersion' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // プロセッサ
            'processor' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // メモリ
            'memory' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // グラフィック
            'graphics' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ディスク空き容量
            'diskFreeSpace' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 備考
            'note' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
        ],
        // Mac対応環境
        'macSupportedEnvironment' => [
            // OSバージョン
            'osVersion' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // プロセッサ
            'processor' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // メモリ
            'memory' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // グラフィック
            'graphics' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // ディスク空き容量
            'diskFreeSpace' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // 備考
            'note' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
        ],
        // CERO
        'cero' => [
            // CERO区分
            'classification' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
            // CEROコンテンツアイコン
            'contentIcons' =>
                ['pc' => false, 'sp' => false, 'android_app' => false, 'pc_channeling' => false, 'sp_channeling' => false, 'client' => true],
        ],
    ];
    
    /**
     * 事前登録情報を設定する
     * リリースステータスが一度でも事前登録完了になっている場合のみ設定してください。
     *
     * @param  mixed $preregistration
     * @return void
     */
    public function setPreregistration($preregistration){
        $this->preregistration = $preregistration;
    }
    
    /**
     * 事前登録が完了しているか確認する
     * リリースステータスが一度でも事前登録完了になっていれば、trueを返します。
     *
     * @param  mixed $default
     * @return void
     */
    public function isPreRegistrationActive(){
        $value = $this->body['isPreRegistrationActive'];
        if(!empty($value)){
            return $value;
        }else{
            return false;
        }
    }

    /**
     * 設定できるゲームタグの取得
     *
     * @return mixed
     */
    public function getVerificationGamesTags()
    {
        return config('forms.Games.gamesTags');
    }

    /**
     * 設定できるゲームタグ説明の取得
     *
     * @param mixed $id
     * @return string
     */
    public function getVerificationGamesTagNotes($id)
    {
        return config('forms.Games.gamesTagNotes.' . $id, '');
    }

    public function getExaminationImages($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('examinationImages', $param1, $param2, $default);
    }

    public function getIntroductionImages($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('introductionImages', $param1, $param2, $default);
    }

    public function getPlatformImages($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('platformImages', $param1, $param2, $default);
    }

    public function getReleaseSite($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('releaseSite', $param1, $param2, $default);
    }

    public function getVerification($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('verification', $param1, $param2, $default);
    }

    public function getGameInformation($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('gameInformation', $param1, $param2, $default);
    }

    public function getCommunity($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('community', $param1, $param2, $default);
    }

    public function getWindowsSupportedEnvironment($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('windowsSupportedEnvironment', $param1, $param2, $default);
    }

    public function getMacSupportedEnvironment($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('macSupportedEnvironment', $param1, $param2, $default);
    }

    public function getCero($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('cero', $param1, $param2, $default);
    }

    public function getLinksmate($param1 = null, $param2 = null, $default = null){
        return $this->getCategoryData('linksmate', $param1, $param2, $default);
    }
    
    /**
     * LinksMateのコピーライトの有無を取得する
     * LinksMateのコピーライトの有無が未設定かつ事前登録申請が完了していれば、事前登録申請のゲーム紹介ページのコピーライトの有無の設定値を返す。
     *
     * @return mixed
     */
    public function getLinksmateIsUseCopyright(){
        $isUseCopyright = $this->getLinksmate('linksmateCopyright','linksmateIsUseCopyright');
        if($isUseCopyright == null && isset($this->preregistration)){
            $isUseCopyright = $this->preregistration->getIntroductionImages('copyright','isUseCopyright');
        }
        return $isUseCopyright;
    }

    /**
     * LinksMateのコピーライトを取得する
     * LinksMateのコピーライトが未設定かつ事前登録申請が完了していれば、事前登録申請のゲーム紹介ページのコピーライトの設定値を返す。
     *
     * @return mixed
     */
    public function getLinksmateCopyrightText(){
        $copyrightText = $this->getLinksmate('linksmateCopyright','linksmateCopyrightText');
        if($copyrightText == null && isset($this->preregistration)){
            $copyrightText = $this->preregistration->getIntroductionImages('copyright','copyrightText','');
        }
        return $copyrightText != null ? $copyrightText : '';
    }

    /**
     * ゲームに設定するタグを取得する
     *
     * @return array
     */
    public function getGamesTags(){
        $gamesTags = $this->getVerification('gamesTags','tags');
        $gamesTags = is_array($gamesTags) ? $gamesTags : [];
        return $gamesTags;
    }
}